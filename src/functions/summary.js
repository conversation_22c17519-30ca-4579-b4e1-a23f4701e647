const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const summaryHandler = require('../handlers/summary-handler')
const helper = require('../common/helper')

app.http('summary', {
  methods: ['POST'],
  authLevel: 'anonymous',
  handler: async (req, context) => {
    context.log(`Http function processed req for url "${req.url}"`)

    const transcript = await req.text()
    if (!transcript || transcript == '') {
      return jsonResponse(`Missing transcript`, HttpStatusCode.BadRequest)
    }

    const conversation = await summaryHandler.identifySpeaker(transcript)
    const summary = await summaryHandler.sumnmaryConversation(conversation)
    const objConversation = helper.parseJSON(conversation)
    const objsummary = helper.parseJSON(summary)
    if (!helper.validateMedicalRecord(objsummary)) {
      let str = process.env.SummaryInfo
      let keys = str.split(',').map((key) => key.trim())
      let obj = {}
      keys.forEach((key) => {
        obj[key] = ''
      })

      // Add default values for additional fields
      obj.vitals = {
        heartRate: 0,
        systolicPressure: 0,
        diastolicPressure: 0,
        respiratoryRate: 0,
        spO2: 0,
        temperature: 0,
      }
      obj.anthropometry = {
        height: '',
        weight: '',
        bmi: 0,
        waistCircumference: '',
      }
      obj.generalphysicalexamination = {
        pallor: false,
        icterus: false,
        cyanosis: false,
        clubbing: false,
        pedalEnema: false,
        pedalEnemaNotes: '',
        lymphadenopathy: false,
        lymphadenopathyNotes: '',
      }
      obj.heent = ''
      obj.systemicexamination = {
        neurologicalExamination: '',
        cardiovascularExamination: '',
        respiratoryExamination: '',
        abdomenExamination: '',
        rheumatologicalExamination: '',
      }

      objsummary = obj
    }
    return jsonResponse({
      conversation: objConversation,
      summary: objsummary,
    })
  },
})
